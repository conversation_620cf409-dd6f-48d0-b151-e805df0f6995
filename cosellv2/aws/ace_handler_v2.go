package aws

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/r3labs/diff/v3"
	"github.com/sugerio/marketplace-service/cosellv2/constants"
	"github.com/sugerio/marketplace-service/cosellv2/hubspot"
	"github.com/sugerio/marketplace-service/cosellv2/notification_event"
	"github.com/sugerio/marketplace-service/partner/aws/ace_fields"
	shared "github.com/sugerio/marketplace-service/pkg"
	"github.com/sugerio/marketplace-service/pkg/bus"
	"github.com/sugerio/marketplace-service/pkg/crud"
	"github.com/sugerio/marketplace-service/pkg/integration/ace"
	sharedHubspot "github.com/sugerio/marketplace-service/pkg/integration/hubspot"
	sharedSalesforce "github.com/sugerio/marketplace-service/pkg/integration/salesforce"
	sconn "github.com/sugerio/marketplace-service/pkg/integration/suger_connector"
	sconnConstants "github.com/sugerio/marketplace-service/pkg/integration/suger_connector/constants"
	"github.com/sugerio/marketplace-service/pkg/log"
	sharedNotificationClient "github.com/sugerio/marketplace-service/pkg/notification_client"
	"github.com/sugerio/marketplace-service/pkg/orm"
	"github.com/sugerio/marketplace-service/pkg/orm/aceengagementinvitation"
	"github.com/sugerio/marketplace-service/pkg/orm/aceopportunity"
	"github.com/sugerio/marketplace-service/pkg/structs"
	sharedTemporal "github.com/sugerio/marketplace-service/pkg/temporal"
	temporalEnums "go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/client"
)

var (
	updateOpportunityActionRequiredFieldWhiteList = map[string]struct{}{
		"Customer.Account.Address.City":              {},
		"Customer.Account.Address.Country":           {},
		"Customer.Account.Address.PostalCode":        {},
		"Customer.Account.Address.StateOrRegion":     {},
		"Customer.Account.Address.StreetAddress":     {},
		"Customer.Account.WebsiteUrl":                {},
		"LifeCycle.TargetCloseDate":                  {},
		"Project.ExpectedMonthlyAWSRevenue.Amount":   {},
		"Project.ExpectedMonthlyAWSRevenue.Currency": {},
		"Project.CustomerBusinessProblem":            {},
		"PartnerOpportunityIdentifier":               {},
	}

	updateOpportunityApprovedFieldBlackList = map[string]struct{}{
		"Customer.Account.Address.Country":    {},
		"Customer.Account.Address.PostalCode": {},
		"Customer.Account.Industry":           {},
		"Customer.Account.WebsiteUrl":         {},
		"Project.CustomerBusinessProblem":     {},
		"PartnerOpportunityIdentifier":        {},
		"Project.Title":                       {},
	}

	updateOpportunityLaunchedFieldBlackList = map[string]struct{}{
		"LifeCycle.TargetCloseDate": {},
		"LifeCycle.Stage":           {},
	}
)

type ListOpportunitiesRequest struct {
	OrganizationID string `json:"orgId" validate:"required" params:"orgId"`
	crud.ListBaseRequest
}

// GetOpportunityRequest represents a request to get an opportunity by id
type GetOpportunityRequest struct {
	OrganizationID string `json:"orgId" validate:"required" params:"orgId"`
	ID             string `json:"id" validate:"required" params:"id"`
}

type ListEngagementInvitationsRequest struct {
	OrganizationID string `json:"orgId" validate:"required" params:"orgId"`
	crud.ListBaseRequest
}

type SyncAcePartnerCentralDataRequest struct {
	OrganizationID string `json:"orgId" validate:"required" params:"orgId"`
}

// CreateOpportunityRequest represents a request to create an opportunity in ACE.
// It contains opportunity ID from source CRM and mapped opportunity by field mapping logic.
type CreateOpportunityRequest struct {
	OrganizationID   string          `validate:"required" params:"orgId"`
	CrmOpportunityID string          `validate:"required" query:"crmOpportunityId"`
	CRMPartner       structs.Partner `validate:"required" query:"crmPartner"`
	Opportunity      ace.Opportunity `json:"opportunity" validate:"required"`
}

// Validate validates the create opportunity request
func (r *CreateOpportunityRequest) Validate() error {
	if len(r.Opportunity.Project.ExpectedCustomerSpend) > 0 {
		spend := r.Opportunity.Project.ExpectedCustomerSpend[0]
		if spend.Amount == "" {
			return errors.New("expected customer spend amount is required")
		}
		if spend.CurrencyCode == "" {
			return errors.New("expected customer spend currency code is required")
		}
		if spend.TargetCompany == "" {
			return errors.New("expected customer spend target company is required")
		}
		if spend.Frequency == "" {
			return errors.New("expected customer spend frequency is required")
		}

		switch spend.Frequency {
		case "Monthly", "None", "Annual":
		default:
			return fmt.Errorf("invalid frequency value: %s, must be one of: Monthly, None, Annual", spend.Frequency)
		}
	}
	return nil
}

// Preprocess preprocesses the opportunity before creating it
func (r *CreateOpportunityRequest) Preprocess() (*ace.Opportunity, error) {
	opp := r.Opportunity

	// Origin should be Partner Referral for all outbound referrals
	opp.Origin = "Partner Referral"

	return &opp, nil
}

// CreateOpportunityResponse represents a response to create an opportunity in ACE.
// It contains the primary key of the opportunity created in suger's db.
type CreateOpportunityResponse struct {
	ID string `json:"id"`
}

// UpdateOpportunityRequest represents a request to update an opportunity in ACE.
// It contains target opportunity ID and mapped opportunity by field mapping logic.
type UpdateOpportunityRequest struct {
	OrganizationID string `validate:"required" params:"orgId"`
	// OpportunityID is ambiguous in our system.
	// It can refer to either the UUID, which serves as the primary key in our database,
	// or the AWS ID, which is the canonical identifier in AWS.
	// In the update opportunity request, it refers to the primary key (UUID) in our database,
	// since we need to be able to re-submit to AWS if the submission attempt fails and the AWS ID has not yet been generated.
	OpportunityID string          `validate:"required" query:"opportunityId"`
	Opportunity   ace.Opportunity `json:"opportunity" validate:"required"`
}

// Validate compare the update opp with current opp to ensure the update request can be accepted by ACE.
func (r *UpdateOpportunityRequest) Validate(currOpp *ace.Opportunity) error {
	// https://docs.aws.amazon.com/partner-central/latest/APIReference/working-with-opportunity-updates.html

	if currOpp == nil {
		return errors.New("currOpp cannot be nil")
	}

	if currOpp.ID == "" || currOpp.LifeCycle.ReviewStatus == ace_fields.StatusPendingSubmission { // re-submit opportunity
		return nil
	}

	if (r.Opportunity.SoftwareRevenue.Value.CurrencyCode == "" && r.Opportunity.SoftwareRevenue.Value.Amount != "") ||
		(r.Opportunity.SoftwareRevenue.Value.CurrencyCode != "" && r.Opportunity.SoftwareRevenue.Value.Amount == "") {
		return errors.New("software revenue currency code and amount must be both empty or both non-empty")
	}

	if currOpp.LifeCycle.ReviewStatus == ace_fields.StatusSubmitted ||
		currOpp.LifeCycle.ReviewStatus == ace_fields.StatusInReview {
		return errors.New("cannot update opportunity: no changes can be made until the review process is complete")
	}

	updateDiff, err := diff.Diff(*currOpp, r.Opportunity)
	if err != nil {
		return err
	}

	filteredDiff := make([]diff.Change, 0, len(updateDiff))
	for _, c := range updateDiff {
		if !isZeroValue(c.To) {
			filteredDiff = append(filteredDiff, c)
		}
	}
	if len(filteredDiff) == 0 {
		return errors.New("no changes were made")
	}

	updateFields := make([]string, 0, len(filteredDiff))
	for _, c := range filteredDiff {
		updateFields = append(updateFields, strings.Join(c.Path, "."))
	}

	switch currOpp.LifeCycle.Stage {
	case ace_fields.StageLaunched:
		for _, field := range updateFields {
			if _, found := updateOpportunityLaunchedFieldBlackList[field]; found {
				return fmt.Errorf("disallowed field %s for update", field)
			}
		}
	}

	switch currOpp.LifeCycle.ReviewStatus {
	case ace_fields.StatusActionRequired:
		for _, field := range updateFields {
			if _, found := updateOpportunityActionRequiredFieldWhiteList[field]; !found {
				return fmt.Errorf("disallowed field %s for update", field)
			}
		}
	case ace_fields.StatusApproved:
		for _, field := range updateFields {
			if _, found := updateOpportunityApprovedFieldBlackList[field]; found {
				return fmt.Errorf("disallowed field %s for update", field)
			}
		}
	default:
		return errors.New("cannot update opportunity")
	}

	return nil
}

// OutboundSyncRequest represents a request to sync opportunities from ACE Partner Central.
type OutboundSyncRequest struct {
	OrganizationID string          `validate:"required" params:"orgId"`
	CrmPartner     structs.Partner `validate:"required" query:"crmPartner"`
}

// EngagementInvitationRequest represents a request to respond to an engagement invitation.
type EngagementInvitationRequest struct {
	OrganizationID          string `validate:"required" params:"orgId"`
	EngagementInvitationARN string `validate:"required" json:"engagementInvitationArn"`
	Accept                  *bool  `validate:"required" json:"accept"`
	RejectReason            string `json:"rejectReason"`
}

// BatchCreateOpportunityRequest represents a request to create a batch of opportunities.
type BatchCreateOpportunityRequest struct {
	OrganizationID string                     `validate:"required" params:"orgId"`
	Opportunities  map[string]ace.Opportunity `json:"opportunities" validate:"required"`
	CRMPartner     structs.Partner            `validate:"required" query:"crmPartner"`
}

// BatchCreateOpportunityResult represents the result of creating a single opportunity
type BatchCreateOpportunityResult struct {
	CRMOpportunityID string `json:"crmOpportunityId"`
	Success          bool   `json:"success"`
	Error            string `json:"error,omitempty"`
	ID               string `json:"id,omitempty"`
}

// BatchCreateOpportunityResponse represents the response for batch creating opportunities
type BatchCreateOpportunityResponse struct {
	Results []BatchCreateOpportunityResult `json:"results"`
}

// AceOpportunityResponse wraps orm.AceOpportunity to provide custom JSON marshaling
type AceOpportunityResponse struct {
	*orm.AceOpportunity
}

// MarshalJSON implements json.Marshaler interface
func (r *AceOpportunityResponse) MarshalJSON() ([]byte, error) {
	type Alias orm.AceOpportunity
	aux := &struct {
		*Alias
		AWSCreatedDate      *time.Time `json:"aws_created_date,omitempty"`
		AWSLastModifiedDate *time.Time `json:"aws_last_modified_date,omitempty"`
		TargetCloseDate     *time.Time `json:"target_close_date,omitempty"`
		EffectiveDate       *time.Time `json:"effective_date,omitempty"`
		ExpirationDate      *time.Time `json:"expiration_date,omitempty"`
		AWSTargetCloseDate  *time.Time `json:"aws_target_close_date,omitempty"`
	}{
		Alias: (*Alias)(r.AceOpportunity),
	}

	// Only include non-zero time values
	if !r.AWSCreatedDate.IsZero() {
		aux.AWSCreatedDate = &r.AWSCreatedDate
	}
	if !r.AWSLastModifiedDate.IsZero() {
		aux.AWSLastModifiedDate = &r.AWSLastModifiedDate
	}
	if !r.TargetCloseDate.IsZero() {
		aux.TargetCloseDate = &r.TargetCloseDate
	}
	if !r.EffectiveDate.IsZero() {
		aux.EffectiveDate = &r.EffectiveDate
	}
	if !r.ExpirationDate.IsZero() {
		aux.ExpirationDate = &r.ExpirationDate
	}
	if !r.AWSTargetCloseDate.IsZero() {
		aux.AWSTargetCloseDate = &r.AWSTargetCloseDate
	}

	return json.Marshal(aux)
}

type AceHandlerV2 interface {
	// CreateOpportunity creates a new opportunity in suger's db.
	CreateOpportunity(ctx context.Context, req *CreateOpportunityRequest) (*CreateOpportunityResponse, error)

	// UpdateOpportunity updates an existing opportunity in suger's db.
	UpdateOpportunity(ctx context.Context, req *UpdateOpportunityRequest) error

	// BatchCreateOpportunity creates a batch of opportunities and triggers submission events to ACE.
	BatchCreateOpportunity(ctx context.Context, req *BatchCreateOpportunityRequest) (*BatchCreateOpportunityResponse, error)

	// ListOpportunities returns a list of opportunities stored in suger's db.
	ListOpportunities(ctx context.Context, req *ListOpportunitiesRequest) (*crud.ListBaseResponse[*orm.AceOpportunity], error)

	// GetOpportunity returns an opportunity by id
	GetOpportunity(ctx context.Context, req *GetOpportunityRequest) (*AceOpportunityResponse, error)

	// ListEngagementInvitations returns a list of engagement invitations stored in suger's db.
	ListEngagementInvitations(ctx context.Context, req *ListEngagementInvitationsRequest) (*crud.ListBaseResponse[*orm.AceEngagementInvitation], error)

	// SyncOpportunitiesInBackground starts a temporal workflow to sync opportunities from ACE Partner Central.
	SyncOpportunitiesInBackground(ctx context.Context, req *SyncAcePartnerCentralDataRequest) error

	// SyncAllInBackground starts a temporal workflow to sync all data from ACE Partner Central.
	SyncAllInBackground(ctx context.Context, req *SyncAcePartnerCentralDataRequest) error

	// SyncEngagementInvitationsInBackground starts a temporal workflow to sync engagement invitations from ACE Partner Central.
	SyncEngagementInvitationsInBackground(ctx context.Context, req *SyncAcePartnerCentralDataRequest) error

	// OutboundSyncOpportunity starts a temporal workflow to sync opportunities for organization with Partner Central API enabled.
	OutboundSyncOpportunity(ctx context.Context, req *OutboundSyncRequest) error

	// RespondToEngagementInvitation responds to an engagement invitation.
	RespondToEngagementInvitation(ctx context.Context, req *EngagementInvitationRequest) error
}

type aceHandlerV2 struct {
	repos                   AceRepositories
	ormClient               *orm.Client
	temporalClient          client.Client
	salesforceClientFactory sharedSalesforce.ClientFactory
	hubspotClientFactory    sharedHubspot.ClientFactory
	notificationClient      sharedNotificationClient.SQSEventSender
	eventBus                bus.EventBus
}

// NewAceHandlerV2 creates a new instance of AceHandlerV2.
func NewAceHandlerV2(
	repos AceRepositories,
	ormClient *orm.Client,
	temporalClient client.Client,
	salesforceClientFactory sharedSalesforce.ClientFactory,
	hubspotClientFactory sharedHubspot.ClientFactory,
	notificationClient sharedNotificationClient.SQSEventSender,
	eventBus bus.EventBus) AceHandlerV2 {

	return &aceHandlerV2{
		repos:                   repos,
		ormClient:               ormClient,
		temporalClient:          temporalClient,
		salesforceClientFactory: salesforceClientFactory,
		hubspotClientFactory:    hubspotClientFactory,
		notificationClient:      notificationClient,
		eventBus:                eventBus,
	}
}

func (a *aceHandlerV2) GetOpportunity(ctx context.Context,
	req *GetOpportunityRequest) (*AceOpportunityResponse, error) {
	if req == nil || req.OrganizationID == "" {
		return nil, fmt.Errorf("organization id is required")
	}

	data, err := a.ormClient.AceOpportunity.Query().
		Where(
			aceopportunity.ID(req.ID),
			aceopportunity.OrganizationID(req.OrganizationID),
		).
		WithContactLinks(func(q *orm.AceContactLinkQuery) {
			q.WithContact()
		}).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	return &AceOpportunityResponse{AceOpportunity: data}, nil
}

// ListOpportunities returns a list of opportunities stored in suger's db.
func (a *aceHandlerV2) ListOpportunities(ctx context.Context,
	req *ListOpportunitiesRequest) (*crud.ListBaseResponse[*orm.AceOpportunity], error) {
	if req == nil || req.OrganizationID == "" {
		return nil, fmt.Errorf("organization id is required")
	}

	query := crud.NewQuery[*orm.AceOpportunity](a.ormClient)
	data, err := query.List(ctx, &crud.ListBaseRequest{
		PageSize:   req.PageSize,
		PageNumber: req.PageNumber,
		Filter:     req.Filter,
		Sorts:      req.Sorts,
	}, crud.WithOrganizationID(req.OrganizationID))
	if err != nil {
		return nil, err
	}
	return data, nil
}

// ListEngagementInvitations returns a list of engagement invitations stored in suger's db.
func (a *aceHandlerV2) ListEngagementInvitations(ctx context.Context,
	req *ListEngagementInvitationsRequest) (*crud.ListBaseResponse[*orm.AceEngagementInvitation], error) {
	if req == nil || req.OrganizationID == "" {
		return nil, fmt.Errorf("organization id is required")
	}
	query := crud.NewQuery[*orm.AceEngagementInvitation](a.ormClient)
	if len(req.ListBaseRequest.Sorts) == 0 {
		req.ListBaseRequest.Sorts = []crud.SortOption{crud.SortDesc("invitation_date")}
	}
	data, err := query.List(ctx, &req.ListBaseRequest, crud.WithOrganizationID(req.OrganizationID))
	if err != nil {
		return nil, err
	}
	return data, nil
}

// SyncOpportunitiesInBackground starts a temporal workflow to sync opportunities from ACE Partner Central.
func (a *aceHandlerV2) SyncOpportunitiesInBackground(ctx context.Context,
	req *SyncAcePartnerCentralDataRequest) error {
	if req == nil || req.OrganizationID == "" {
		return fmt.Errorf("organization id is required")
	}

	wfID := fmt.Sprintf(
		sharedTemporal.WorkflowIdTemplate_FetchLatestAWSOpportunities_Organization,
		req.OrganizationID,
	)
	options := client.StartWorkflowOptions{
		ID:                       wfID,
		TaskQueue:                constants.TaskQueueACE,
		WorkflowExecutionTimeout: 120 * time.Minute,
		WorkflowRunTimeout:       60 * time.Minute,
		WorkflowIDReusePolicy:    temporalEnums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE,
	}
	_, err := sharedTemporal.StartWorkflow_NonOverride(
		ctx,
		a.temporalClient,
		&options,
		FetchACEOpportunitiesFullyForOrg,
		req.OrganizationID,
	)
	return err
}

// SyncAllInBackground starts a temporal workflow to sync all data from ACE Partner Central.
func (a *aceHandlerV2) SyncAllInBackground(ctx context.Context,
	req *SyncAcePartnerCentralDataRequest) error {
	err := a.SyncOpportunitiesInBackground(ctx, req)
	if err != nil {
		return err
	}

	err = a.SyncEngagementInvitationsInBackground(ctx, req)
	if err != nil {
		return err
	}

	return nil
}

// SyncEngagementInvitationsInBackground starts a temporal workflow to sync engagement invitations from ACE Partner Central.
func (a *aceHandlerV2) SyncEngagementInvitationsInBackground(ctx context.Context,
	req *SyncAcePartnerCentralDataRequest) error {
	if req == nil || req.OrganizationID == "" {
		return fmt.Errorf("organization id is required")
	}

	wfID := fmt.Sprintf(
		sharedTemporal.WorkflowIdTemplate_FetchLatestAWSEngagementInvitations_Organization,
		req.OrganizationID,
	)
	options := client.StartWorkflowOptions{
		ID:                       wfID,
		TaskQueue:                constants.TaskQueueACE,
		WorkflowExecutionTimeout: 120 * time.Minute,
		WorkflowRunTimeout:       60 * time.Minute,
		WorkflowIDReusePolicy:    temporalEnums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE,
	}
	_, err := sharedTemporal.StartWorkflow_NonOverride(
		ctx,
		a.temporalClient,
		&options,
		FetchACEEngagementInvitationFullyForOrg,
		req.OrganizationID,
	)
	return err
}

// CreateOpportunity creates a new opportunity in the database and emits an event to submit the opportunity to the ACE.
func (a *aceHandlerV2) CreateOpportunity(ctx context.Context, req *CreateOpportunityRequest) (*CreateOpportunityResponse, error) {
	logger := log.GetLogger(ctx).With("method", "AceHandlerV2.CreateOpportunity")
	if req == nil {
		return nil, errors.New("request cannot be nil")
	}
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// Preprocess opportunity
	opp, err := req.Preprocess()
	if err != nil {
		return nil, err
	}

	res := &CreateOpportunityResponse{}
	orgID := req.OrganizationID

	// Create the Suger Referral in DB.
	oppID, err := a.repos.CreateOpportunity(ctx, orgID, opp)
	if err != nil {
		return res, err
	}

	// populate response id
	res.ID = oppID

	// Populate the partner opportunity identifier.
	// This field uniquely identifies an opportunity in the CRM system.
	// Use the database primary key instead of the SFDC opportunity ID,
	// since a single opportunity  may be shared multiple times.
	req.Opportunity.PartnerOpportunityIdentifier = oppID

	referralOpp := convertACEToReferralOpp(&req.Opportunity)
	referralOpp.PartnerCrmUniqueIdentifier = req.CrmOpportunityID

	referral := &structs.Referral{
		Id:             oppID,
		OrganizationId: orgID,
		Partner:        structs.Partner_AWS,
		Info: &structs.ReferralInfo{
			AceOpportunity: referralOpp,
		},
		Name: referralOpp.PartnerProjectTitle,
	}

	// Create CRM record in Salesforce or Hubspot,
	// This is a blocking call since we need to wait for the CRM record to be created before submitting to ACE
	// so that user can see the referral in Salesforce in order to track the status or retry when submitting to ACE fails
	var sfdcID string
	switch req.CRMPartner {
	case structs.Partner_SALESFORCE:
		sfdcClient, err := a.salesforceClientFactory.New(ctx, req.OrganizationID)
		if err != nil {
			return res, err
		}
		if sfdcClient.SugerConnectorInstalled() {
			sugerConnectorClient := sconn.NewSugerConnectorClient(sfdcClient)
			sfdcID, err = sugerConnectorClient.UpsertAWSReferralWithPayload(ctx, referral, nil, map[string]any{
				sconnConstants.Field_CreationStatus: sconnConstants.ReferralActionStatus_Pending,
			})
			if err != nil {
				logger.Error("failed to create Salesforce record", "error", err)
				// fail fast, let user aware of the salesforce referral creation failure
				return res, err
			}
		}
	case structs.Partner_HUBSPOT:
		hubspotClient, err := a.hubspotClientFactory.New(ctx, req.OrganizationID)
		if err != nil {
			return res, err
		}
		integration := hubspotClient.Integration()
		if integration != nil {
			if integration.DealReferralStateField != "" {
				err = hubspot.AppendReferralState(ctx,
					req.CrmOpportunityID,
					integration.DealReferralStateField,
					hubspotClient,
					"AWS")
				if err != nil {
					// This should not return an error as it is not as critical as the Salesforce referral creation failure
					logger.Error("failed to update hubspot referral state", "error", err)
				}
			}
		} else {
			logger.Warn("hubspot integration is nil", "orgID", req.OrganizationID)
		}
	default:
		logger.Error("invalid crmPartner", "crmPartner", req.CRMPartner)
	}

	// Set MetaInfo
	err = a.repos.UpdateOpportunityMetaInfoByID(ctx, orgID, oppID, &structs.ReferralMetaInfo{
		LastSyncTime:         shared.Ptr(time.Now()),
		CRMOpportunityID:     shared.Ptr(req.CrmOpportunityID),
		CRMPartner:           shared.Ptr(string(req.CRMPartner)),
		SalesforceReferralId: shared.PtrIfNotZero(sfdcID),
		CreationStatus:       shared.Ptr(structs.ReferralActionStatusType_Pending),
	})
	if err != nil {
		logger.Error("failed to update opportunity metaInfo", "error", err)
		return res, errors.New("failed to update opportunity metaInfo")
	}

	// Emit an event for opportunity submission
	err = a.eventBus.EmitEvent(ctx, AceCustomEventTypeSubmitOpportunity, NewSubmitOpportunityEvent(orgID, oppID))
	if err != nil {
		return res, err
	}

	// Send notification event
	err = notification_event.SendCreateReferralEvent(ctx, a.notificationClient, referral, structs.NotificationChannel_SNS)
	if err != nil {
		logger.Error("failed to send notification event", "error", err)
	}

	return res, nil
}

// UpdateOpportunity updates an existing opportunity in the database and emits an event to update the opportunity to the ACE.
func (a *aceHandlerV2) UpdateOpportunity(ctx context.Context, req *UpdateOpportunityRequest) error {
	logger := log.GetLogger(ctx).With("method", "AceHandlerV2.UpdateOpportunity")

	orgID := req.OrganizationID
	oppID := req.OpportunityID
	opp := req.Opportunity

	currOpp, err := a.repos.FindOpportunity(ctx, orgID, oppID)
	if err != nil {
		return err
	}
	err = req.Validate(currOpp)
	if err != nil {
		return err
	}

	err = a.repos.UpdateOpportunityByID(ctx, orgID, oppID, &opp)
	if err != nil {
		return err
	}

	oppMetainfo, err := a.repos.GetOpportunityMetaInfo(ctx, orgID, oppID)
	if err != nil {
		return err
	}

	// Retry submission if previous attempt failed
	if oppMetainfo.CreationStatus != nil && *oppMetainfo.CreationStatus == structs.ReferralActionStatusType_Failed {
		err = a.eventBus.EmitEvent(ctx, AceCustomEventTypeSubmitOpportunity, NewSubmitOpportunityEvent(orgID, oppID))
		if err != nil {
			return err
		}
		return nil
	}

	referralOpp := convertACEToReferralOpp(&opp)

	referral := &structs.Referral{
		Id:             oppID,
		OrganizationId: orgID,
		Partner:        structs.Partner_AWS,
		Info: &structs.ReferralInfo{
			AceOpportunity: referralOpp,
		},
		Name: referralOpp.PartnerProjectTitle,
	}

	if oppMetainfo.CRMPartner == nil {
		logger.Warn("UpdateOpportunity crmPartner is nil", "orgID", orgID, "oppID", oppID)
	} else {
		// Update CRM record status in Salesforce or Hubspot
		switch structs.Partner(*oppMetainfo.CRMPartner) {
		case structs.Partner_SALESFORCE:
			sfdcClient, err := a.salesforceClientFactory.New(ctx, req.OrganizationID)
			if err != nil {
				return err
			}
			if sfdcClient.SugerConnectorInstalled() {
				sugerConnectorClient := sconn.NewSugerConnectorClient(sfdcClient)
				_, err = sugerConnectorClient.UpsertAWSReferralWithPayload(ctx, referral, oppMetainfo, map[string]any{
					sconnConstants.Field_UpdateStatus:         sconnConstants.ReferralActionStatus_Pending,
					sconnConstants.Field_UpdateFailureMessage: "",
				})
				if err != nil {
					logger.Error("failed to update Salesforce record", "error", err)
					// fail fast, let user aware of the salesforce referral update failure
					return err
				}
			}
		case structs.Partner_HUBSPOT:
			// TODO: update status in hubspot
		default:
			logger.Error("invalid crmPartner", "crmPartner", *oppMetainfo.CRMPartner)
		}
	}

	// Update MetaInfo
	err = a.repos.UpdateOpportunityMetaInfo(ctx, orgID, oppID, &structs.ReferralMetaInfo{
		LastSyncTime:         shared.Ptr(time.Now()),
		UpdateStatus:         shared.Ptr(structs.ReferralActionStatusType_Pending),
		UpdateFailureMessage: shared.Ptr(""),
	})
	if err != nil {
		logger.Error("failed to update opportunity metaInfo", "error", err)
		return errors.New("failed to update opportunity metaInfo")
	}

	// Emit an event for opportunity submission
	err = a.eventBus.EmitEvent(ctx, AceCustomEventTypeUpdateOpportunity, NewUpdateOpportunityEvent(orgID, oppID))
	if err != nil {
		return err
	}

	// Send notification event
	err = notification_event.SendUpdateReferralEvent(ctx, a.notificationClient, referral, structs.NotificationChannel_SNS)
	if err != nil {
		logger.Error("failed to send notification event", "error", err)
	}

	return nil
}

// BatchCreateOpportunity creates a batch of opportunities in the database and emits the corresponding events to submit them to ACE.
func (a *aceHandlerV2) BatchCreateOpportunity(ctx context.Context, req *BatchCreateOpportunityRequest) (*BatchCreateOpportunityResponse, error) {
	logger := log.GetLogger(ctx).With("method", "AceHandlerV2.BatchCreateOpportunity")

	if len(req.Opportunities) > 20 {
		return nil, errors.New("batch create opportunity is limited to 20 opportunities at a time")
	}

	orgID := req.OrganizationID
	results := make([]BatchCreateOpportunityResult, 0, len(req.Opportunities))

	for crmOpportunityID, opportunity := range req.Opportunities {
		result := BatchCreateOpportunityResult{
			CRMOpportunityID: crmOpportunityID,
		}

		resp, err := a.CreateOpportunity(ctx, &CreateOpportunityRequest{
			OrganizationID:   orgID,
			CrmOpportunityID: crmOpportunityID,
			Opportunity:      opportunity,
			CRMPartner:       req.CRMPartner,
		})

		if err != nil {
			logger.Error("failed to create opportunity",
				"crmOpportunityId", crmOpportunityID,
				"error", err)
			result.Success = false
			result.Error = err.Error()
		} else {
			result.Success = true
			result.ID = resp.ID
		}

		results = append(results, result)
	}

	response := &BatchCreateOpportunityResponse{
		Results: results,
	}

	return response, nil
}

func (a *aceHandlerV2) OutboundSyncOpportunity(ctx context.Context, req *OutboundSyncRequest) error {

	wfID := fmt.Sprintf(
		sharedTemporal.WorkflowIdTemplate_OutboundSyncAWSOpportunities_Organization,
		req.OrganizationID,
	)
	options := client.StartWorkflowOptions{
		ID:                       wfID,
		TaskQueue:                constants.TaskQueueACE,
		WorkflowExecutionTimeout: 120 * time.Minute,
		WorkflowRunTimeout:       60 * time.Minute,
		WorkflowIDReusePolicy:    temporalEnums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE,
	}
	_, err := sharedTemporal.StartWorkflow_NonOverride(
		ctx,
		a.temporalClient,
		&options,
		OutboundSyncOpportunitiesForOrg,
		req.OrganizationID,
		req.CrmPartner,
	)
	return err
}

func (a *aceHandlerV2) RespondToEngagementInvitation(ctx context.Context, req *EngagementInvitationRequest) error {
	logger := log.GetLogger(ctx).With("method", "AceHandlerV2.RespondToEngagementInvitation")
	invitation, err := a.repos.FindEngagementInvitation(ctx, req.OrganizationID, req.EngagementInvitationARN)
	if err != nil {
		logger.Error("failed to find engagement invitation", "error", err, "arn", req.EngagementInvitationARN)
		return fmt.Errorf("failed to find engagement invitation")
	}
	if invitation == nil {
		logger.Error("failed to find engagement invitation", "arn", req.EngagementInvitationARN)
		return fmt.Errorf("no engagement invitation found")
	}
	if invitation.Status != string(aceengagementinvitation.StatusPENDING) {
		return fmt.Errorf("invalid status of engagement invitation")
	}
	if req.Accept == nil {
		return errors.New("request is missing required field: Accept")
	}

	if *req.Accept {
		invitation.Status = string(aceengagementinvitation.StatusACCEPTED)
	} else {
		invitation.Status = string(aceengagementinvitation.StatusREJECTED)
	}
	err = a.repos.UpdateEngagementInvitation(ctx, req.OrganizationID, invitation)
	if err != nil {
		logger.Error("failed to update engagement invitation", "error", err)
		return fmt.Errorf("failed to update engagement invitation status")
	}

	return a.eventBus.EmitEvent(ctx, AceCustomEventTypeEngagementInvitation, NewEngagementInvitationEvent(req.EngagementInvitationARN, req.OrganizationID, *req.Accept, req.RejectReason))
}

// isZeroValue returns true if v is a zero value
func isZeroValue(v interface{}) bool {
	if v == nil {
		return true
	}
	rv := reflect.ValueOf(v)
	switch rv.Kind() {
	case reflect.Ptr, reflect.Interface:
		return rv.IsNil()
	case reflect.Slice, reflect.Map, reflect.Array, reflect.String:
		return rv.Len() == 0
	default:
		return reflect.DeepEqual(v, reflect.Zero(rv.Type()).Interface())
	}
}
